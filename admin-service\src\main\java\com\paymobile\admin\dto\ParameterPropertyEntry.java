package com.paymobile.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "Property of a parameter")
public class ParameterPropertyEntry implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "Identifier of the parameter")
    private int parameterId;
    @Schema(description = "Property name")
    private String name;
    @Schema(description = "Property value")
    private String valueString;

    public int getParameterId() { return parameterId; }
    public void setParameterId(int parameterId) { this.parameterId = parameterId; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    public String getValueString() { return valueString; }
    public void setValueString(String valueString) { this.valueString = valueString; }
}
