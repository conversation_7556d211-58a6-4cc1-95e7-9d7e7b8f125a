package com.paymobile.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "Localized text value")
public class I18nEntry implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "ISO language code", example = "en")
    private String languageCode;
    @Schema(description = "Translated value", example = "Payment")
    private String value;

    public I18nEntry() {}
    public I18nEntry(String languageCode, String value) {
        this.languageCode = languageCode;
        this.value = value;
    }
    public String getLanguageCode() { return languageCode; }
    public void setLanguageCode(String languageCode) { this.languageCode = languageCode; }
    public String getValue() { return value; }
    public void setValue(String value) { this.value = value; }
}
