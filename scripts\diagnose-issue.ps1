# PayMobile Operation Admin - Issue Diagnosis Script (ASCII-safe)
# Purpose: Diagnose ServiceConstructionException and environment readiness on WildFly 15

[CmdletBinding()]
param()

function Write-Info($msg) { Write-Host $msg -ForegroundColor Cyan }
function Write-Step($msg) { Write-Host $msg -ForegroundColor Yellow }
function Write-Ok($msg)   { Write-Host "[OK]  $msg" -ForegroundColor Green }
function Write-Err($msg)  { Write-Host "[ERR] $msg" -ForegroundColor Red }
function Write-Note($msg) { Write-Host "      $msg" -ForegroundColor White }

function Test-Url {
    param(
        [Parameter(Mandatory=$true)][string]$Url,
        [int]$TimeoutSec = 10
    )
    try {
        $resp = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSec -UseBasicParsing
        return @{ Ok = $true; Code = $resp.StatusCode; Content = $resp.Content }
    } catch {
        return @{ Ok = $false; Error = $_.Exception.Message }
    }
}

Clear-Host
Write-Info "=== PayMobile Operation Admin - Issue Diagnosis ==="
Write-Host ""

# Step 1: WildFly availability
Write-Step "1) Testing WildFly availability on http://localhost:8080 ..."
$wf = Test-Url -Url "http://localhost:8080" -TimeoutSec 5
if ($wf.Ok) { Write-Ok ("WildFly is running (Status: {0})" -f $wf.Code) }
else {
    Write-Err "WildFly is not accessible at http://localhost:8080"
    Write-Note "Please start WildFly (standalone.bat) before continuing."
    exit 1
}

# Step 2: admin-web deployment (Swagger UI)
Write-Step "`n2) Testing admin-web deployment (Swagger UI) ..."
$swagger = Test-Url -Url "http://localhost:8080/admin-web/swagger-ui/index.html" -TimeoutSec 5
if ($swagger.Ok) { Write-Ok "admin-web is deployed (Swagger UI accessible)" }
else {
    Write-Err "admin-web is not deployed or not accessible"
    Write-Note "Deploy admin-web.war to WildFly deployments directory."
}

# Step 3: OpenAPI endpoint
Write-Step "`n3) Testing OpenAPI endpoint ..."
# Try both common paths
$openapiCandidates = @(
    "http://localhost:8080/admin-web/api/openapi.json",
    "http://localhost:8080/admin-web/api/openapi",
    "http://localhost:8080/admin-web/openapi" # fallback
)
$openapiOk = $false
foreach ($c in $openapiCandidates) {
    $o = Test-Url -Url $c -TimeoutSec 5
    if ($o.Ok) {
        Write-Ok ("OpenAPI endpoint is accessible: {0}" -f $c)
        $openapiOk = $true
        break
    }
}
if (-not $openapiOk) { Write-Err "OpenAPI endpoint is not accessible on known paths." }

# Step 4: WSDL URLs (from likely configs)
Write-Step "`n4) Testing WSDL URLs ..."
$wsdlUrls = @(
    "http://localhost:8080/operations?wsdl",
    "http://localhost:8080/businessservices/operations/?wsdl",
    "http://wsdler-test.tamkeen.com.ye:8080/businessservices/operations/?wsdl"
)
foreach ($url in $wsdlUrls) {
    Write-Info ("  Testing: {0}" -f $url)
    $r = Test-Url -Url $url -TimeoutSec 10
    if ($r.Ok) {
        Write-Ok ("Accessible (Status: {0})" -f $r.Code)
        # Best-effort metadata extraction
        if ($r.Content -match 'targetNamespace="([^"]+)"') {
            Write-Note ("Target Namespace: {0}" -f $matches[1])
        }
        if ($r.Content -match '<wsdl:service[^>]+name="([^"]+)"') {
            Write-Note ("Service Name: {0}" -f $matches[1])
        }
    } else {
        Write-Err ("Not accessible: {0}" -f $r.Error)
    }
}

# Step 5: Check WildFly/application.properties for operations.wsdlUrl
Write-Step "`n5) Checking WildFly configuration for application.properties ..."
$wildflyHome = $env:WILDFLY_HOME
if ([string]::IsNullOrWhiteSpace($wildflyHome)) {
    # Try to guess common paths if env var not set
    $candidates = @(
        "C:\Users\<USER>\wildfly-15.0.1.Final",
        "C:\wildfly-15.0.1.Final",
        "G:\wildfly-15.0.1.Final"
    )
    foreach ($cand in $candidates) {
        if (Test-Path $cand) { $wildflyHome = $cand; break }
    }
}
if ([string]::IsNullOrWhiteSpace($wildflyHome)) {
    Write-Err "WILDFLY_HOME is not set and could not be inferred."
    Write-Note "Set it before running: `$env:WILDFLY_HOME='C:\path\to\wildfly-15.0.1.Final'"
} else {
    $configFile = Join-Path $wildflyHome "standalone\configuration\application.properties"
    if (Test-Path $configFile) {
        Write-Ok ("Found application.properties at: {0}" -f $configFile)
        $content = Get-Content -LiteralPath $configFile -ErrorAction SilentlyContinue
        $wsdlConfig = $content | Where-Object { $_ -match '^\s*operations\.wsdlUrl\s*=' }
        if ($wsdlConfig) {
            Write-Note ("Configured WSDL URL: {0}" -f ($wsdlConfig -join '; '))
        } else {
            Write-Note "No 'operations.wsdlUrl' key found."
        }
    } else {
        Write-Err ("application.properties not found at: {0}" -f $configFile)
        Write-Note "Copy scripts\wildfly-application.properties to this location."
    }
}

Write-Info "`n=== Diagnosis Complete ==="
Write-Step  "Next steps:"
Write-Note  "1) If WSDL URLs are not accessible, update configuration."
Write-Note  "2) Run scripts\configure-wildfly.cli to set system properties."
Write-Note  "3) Copy scripts\wildfly-application.properties to WildFly config."
Write-Note  "4) Test with scripts\post-provision.ps1."

