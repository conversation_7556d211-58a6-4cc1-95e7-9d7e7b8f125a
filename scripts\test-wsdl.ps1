Param(
    [Parameter(Mandatory=$true)]
    [string]$WsdlUrl,
    [int]$TimeoutSeconds = 30
)

Write-Host "Testing WSDL accessibility: $WsdlUrl" -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri $WsdlUrl -TimeoutSec $TimeoutSeconds -UseBasicParsing
    Write-Host "✓ Status: $($response.StatusCode) $($response.StatusDescription)" -ForegroundColor Green
    Write-Host "✓ Content-Type: $($response.Headers['Content-Type'])" -ForegroundColor Green
    Write-Host "✓ Content-Length: $($response.Content.Length) bytes" -ForegroundColor Green
    
    Write-Host "`nFirst 500 characters of WSDL:" -ForegroundColor Cyan
    $preview = $response.Content.Substring(0, [Math]::Min(500, $response.Content.Length))
    Write-Host $preview -ForegroundColor White
    
    # Check for key WSDL elements
    if ($response.Content -match 'targetNamespace="([^"]+)"') {
        Write-Host "`n✓ Target Namespace: $($matches[1])" -ForegroundColor Green
    }
    
    if ($response.Content -match '<wsdl:service[^>]+name="([^"]+)"') {
        Write-Host "✓ Service Name: $($matches[1])" -ForegroundColor Green
    }
    
    if ($response.Content -match '<wsdl:port[^>]+name="([^"]+)"') {
        Write-Host "✓ Port Name: $($matches[1])" -ForegroundColor Green
    }
    
    Write-Host "`n✓ WSDL test PASSED" -ForegroundColor Green
    exit 0
    
} catch {
    Write-Host "✗ WSDL test FAILED: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
    
    exit 1
}
