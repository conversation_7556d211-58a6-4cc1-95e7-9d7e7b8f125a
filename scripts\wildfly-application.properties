# PayMobile Operation Admin Configuration for WildFly Deployment
# Place this file in $WILDFLY_HOME/standalone/configuration/application.properties

# SOAP Configuration - Updated for WildFly environment
# Note: Using localhost URLs since SOAP services should be running on same server
operations.wsdlUrl=http://localhost:8080/businessservices/operations/?wsdl
operations.endpointUrl=http://localhost:8080/businessservices/operations/
operations.connectTimeoutMs=15000
operations.readTimeoutMs=30000

# Alternative SOAP Configuration (if using embedded WSDL)
# operations.wsdlUrl=classpath:wsdl/operations.wsdl
# operations.endpointUrl=http://wsdler-test.tamkeen.com.ye:8080/businessservices/operations/

# Default Requester Configuration
requester.accessMedium=WEB
requester.domainId=0
requester.accessType=USERNAME
requester.accessValue=ESG_ADMIN
requester.password=CHANGE_ME_TO_ACTUAL_PASSWORD
requester.externalSessionId=${RANDOM_UUID}

# Database Configuration (Server Mode - uses JNDI)
db.jndiName=java:/PayMobileDS
db.upsertEnabled=false

# Application Behavior
continueOnError=false
log.soapPayloads=true

# Additional debugging properties
javax.xml.ws.spi.Provider=com.sun.xml.ws.spi.ProviderImpl
