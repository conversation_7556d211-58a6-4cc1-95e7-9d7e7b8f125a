package com.paymobile.admin.dao;

import com.paymobile.admin.config.ApplicationProperties;
import org.jboss.logging.Logger;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.sql.DataSource;
import java.sql.*;

/**
 * DAO for PARAMETER and PARAMETERPROPERTY using PayMobile patterns.
 * LASTUPDATE formula and idempotency MUST match PayMobile exact behavior.
 */
public class ParameterDao {
    private static final Logger log = Logger.getLogger(ParameterDao.class);

    // LASTUPDATE formula per spec
    private static final String LASTUPDATE = "ROUND((SYSDATE - TO_DATE('1970-01-01','YYYY-MM-DD')) * 86400)";

    private static final String SQL_EXISTS_PARAMETER = "SELECT 1 FROM PARAMETER WHERE PARAMETERID = ?";
    private static final String SQL_INSERT_PARAMETER = "INSERT INTO PARAMETER (PARAMETERID, NAME, STATE, LASTUPDATE) VALUES (?, ?, 'ACTIVE', " + LASTUPDATE + ")";

    private static final String SQL_EXISTS_PARAMETER_PROPERTY = "SELECT 1 FROM PARAMETERPROPERTY WHERE PARAMETERID = ? AND NAME = ?";
    private static final String SQL_INSERT_PARAMETER_PROPERTY = "INSERT INTO PARAMETERPROPERTY (PARAMETERID, NAME, TYPE, VALUESTRING) VALUES (?, ?, 'STRING', ?)";

    private final ApplicationProperties props;

    public ParameterDao(ApplicationProperties props) {
        this.props = props;
    }

    // Protected to allow tests to override the connection acquisition logic
    protected Connection getConnection(boolean serverMode) throws Exception {
        if (serverMode) {
            Context ctx = new InitialContext();
            DataSource ds = (DataSource) ctx.lookup(props.get("db.jndiName", "java:/PayMobileDS"));
            return ds.getConnection();
        } else {
            String url = props.get("db.url", "");
            String user = props.get("db.username", "");
            String pass = props.get("db.password", "");
            String driver = props.get("db.driver", "oracle.jdbc.OracleDriver");
            Class.forName(driver);
            return DriverManager.getConnection(url, user, pass);
        }
    }

    public void upsertParameter(int parameterId, String name, boolean serverMode) throws Exception {
        boolean upsert = props.getBoolean("db.upsertEnabled", false);
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            con = getConnection(serverMode);
            con.setAutoCommit(false);

            // existence check
            ps = con.prepareStatement(SQL_EXISTS_PARAMETER);
            ps.setInt(1, parameterId);
            rs = ps.executeQuery();
            boolean exists = rs.next();
            rs.close(); ps.close();

            if (!exists) {
                ps = con.prepareStatement(SQL_INSERT_PARAMETER);
                ps.setInt(1, parameterId);
                ps.setString(2, name);
                ps.executeUpdate();
                ps.close();
                log.infof("Inserted PARAMETER id=%d name=%s", parameterId, name);
            } else if (upsert) {
                // PayMobile often uses MERGE; implement as update of NAME + LASTUPDATE here if needed
                String sql = "UPDATE PARAMETER SET NAME = ?, LASTUPDATE = " + LASTUPDATE + " WHERE PARAMETERID = ?";
                ps = con.prepareStatement(sql);
                ps.setString(1, name);
                ps.setInt(2, parameterId);
                int n = ps.executeUpdate();
                ps.close();
                log.infof("Updated PARAMETER id=%d rows=%d", parameterId, n);
            } else {
                log.infof("PARAMETER id=%d already exists; skipping", parameterId);
            }

            con.commit();
        } catch (Exception e) {
            if (con != null) try { con.rollback(); } catch (Exception ignore) {}
            throw e;
        } finally {
            if (rs != null) try { rs.close(); } catch (Exception ignore) {}
            if (ps != null) try { ps.close(); } catch (Exception ignore) {}
            if (con != null) try { con.close(); } catch (Exception ignore) {}
        }
    }

    public void upsertParameterProperty(int parameterId, String name, String valueString, boolean serverMode) throws Exception {
        boolean upsert = props.getBoolean("db.upsertEnabled", false);
        Connection con = null;
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            con = getConnection(serverMode);
            con.setAutoCommit(false);

            // existence check
            ps = con.prepareStatement(SQL_EXISTS_PARAMETER_PROPERTY);
            ps.setInt(1, parameterId);
            ps.setString(2, name);
            rs = ps.executeQuery();
            boolean exists = rs.next();
            rs.close(); ps.close();

            if (!exists) {
                ps = con.prepareStatement(SQL_INSERT_PARAMETER_PROPERTY);
                ps.setInt(1, parameterId);
                ps.setString(2, name);
                ps.setString(3, valueString);
                ps.executeUpdate();
                ps.close();
                log.infof("Inserted PARAMETERPROPERTY pid=%d name=%s", parameterId, name);
            } else if (upsert) {
                String sql = "UPDATE PARAMETERPROPERTY SET VALUESTRING = ? WHERE PARAMETERID = ? AND NAME = ?";
                ps = con.prepareStatement(sql);
                ps.setString(1, valueString);
                ps.setInt(2, parameterId);
                ps.setString(3, name);
                int n = ps.executeUpdate();
                ps.close();
                log.infof("Updated PARAMETERPROPERTY pid=%d name=%s rows=%d", parameterId, name, n);
            } else {
                log.infof("PARAMETERPROPERTY pid=%d name=%s exists; skipping", parameterId, name);
            }
            con.commit();
        } catch (Exception e) {
            if (con != null) try { con.rollback(); } catch (Exception ignore) {}
            throw e;
        } finally {
            if (rs != null) try { rs.close(); } catch (Exception ignore) {}
            if (ps != null) try { ps.close(); } catch (Exception ignore) {}
            if (con != null) try { con.close(); } catch (Exception ignore) {}
        }
    }
}
