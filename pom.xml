<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.paymobile</groupId>
    <artifactId>paymobile-operation-admin</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>PayMobile Operation Admin</name>
    <description>Automates adding PayMobile operations end-to-end (SOAP + Oracle)</description>

    <modules>
        <module>admin-service</module>
        <module>admin-cli</module>
        <module>admin-web</module>
        <module>admin-springweb</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <jdk.version>1.8</jdk.version>
        <failOnMissingWebXml>false</failOnMissingWebXml>
        <jaxws.version>2.3.1</jaxws.version>
        <jboss.logging.version>3.4.1.Final</jboss.logging.version>
        <slf4j.version>1.7.36</slf4j.version>
        <logback.version>1.2.11</logback.version>
        <oracle.version>********</oracle.version>
        <wildfly.version>15.0.1.Final</wildfly.version>
        <swagger.version>2.1.10</swagger.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Align versions across modules -->
            <dependency>
                <groupId>org.jboss.logging</groupId>
                <artifactId>jboss-logging</artifactId>
                <version>${jboss.logging.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.ws.rs</groupId>
                <artifactId>javax.ws.rs-api</artifactId>
                <version>2.1.1</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax</groupId>
                <artifactId>javaee-api</artifactId>
                <version>8.0</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>javax.xml.ws</groupId>
                <artifactId>jaxws-api</artifactId>
                <version>${jaxws.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.13.5</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.13.5</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.13.5</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.12.4</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- wsimport for JAX-WS client generation -->
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>jaxws-maven-plugin</artifactId>
                    <version>2.6</version>
                    <configuration>
                        <verbose>true</verbose>
                        <keep>true</keep>
                        <xnocompile>false</xnocompile>
                        <target>2.2</target>
                    </configuration>
                </plugin>
                
                <!-- Maven Shade Plugin for CLI -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.4.1</version>
                </plugin>
                <!-- JAX-WS Plugin لتحويل WSDL إلى Java classes -->
                <plugin>
                    <groupId>com.sun.xml.ws</groupId>
                    <artifactId>jaxws-maven-plugin</artifactId>
                    <version>3.0.0</version>
                    <executions>
                        <execution>
                            <id>wsimport</id>
                            <goals><goal>wsimport</goal></goals>
                            <configuration>
                                <wsdlUrls>
                                    <wsdlUrl>http://wsdler-test.tamkeen.com.ye:8080/businessservices/actor/?wsdl</wsdlUrl>
                                    <wsdlUrl>http://wsdler-test.tamkeen.com.ye:8080/businessservices/operations/?wsdl</wsdlUrl>
                                </wsdlUrls>
                                <sourceDestDir>${project.build.directory}/generated-sources/wsimport</sourceDestDir>
                                <keep>true</keep>
                                <extension>true</extension>
                                <verbose>true</verbose>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
