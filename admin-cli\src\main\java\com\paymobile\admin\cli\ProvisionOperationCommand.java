package com.paymobile.admin.cli;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paymobile.admin.config.ApplicationProperties;
import com.paymobile.admin.dto.OperationDefinition;
import com.paymobile.admin.service.OperationProvisioningService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.Arrays;
import java.util.Properties;

public class ProvisionOperationCommand {
    private static final Logger log = LoggerFactory.getLogger(ProvisionOperationCommand.class);

    public static void main(String[] args) throws Exception {
        String jsonPath = null;
        String propsPath = null;
        for (String arg : args) {
            if (arg.startsWith("--file=")) jsonPath = arg.substring("--file=".length());
            else if (arg.startsWith("--props=")) propsPath = arg.substring("--props=".length());
        }
        if (jsonPath == null) {
            System.err.println("Usage: java -jar admin-cli-*-shaded.jar --file=examples/dttj.json [--props=conf/application.properties]");
            System.exit(1);
        }

        ApplicationProperties props = ApplicationProperties.load(propsPath);

        ObjectMapper mapper = new ObjectMapper();
        OperationDefinition def = mapper.readValue(new File(jsonPath), OperationDefinition.class);

        // Redact sensitive props in logs
        log.info("Starting provisioning for {}.{}, properties loaded with keys: {}", def.getServiceCode(), def.getOperationCode(), props.raw().keySet());

        OperationProvisioningService service = new OperationProvisioningService(props);
        OperationProvisioningService.Summary summary = service.provisionOperation(def);

        log.info("Provisioning completed. created={}, skipped={}, failed={}", summary.created, summary.skipped, summary.failed);

        if (!summary.failed.isEmpty()) {
            System.exit(2);
        }
    }
}
