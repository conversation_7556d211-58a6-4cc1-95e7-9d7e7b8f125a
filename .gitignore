########################################
# OS-specific
########################################
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
# Linux
*~


########################################
# Java / Maven
########################################
# Maven build output
target/
**/target/

# Maven release/versions backups
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

# ⚠️ مهم: لا تُضِف سطرًا لاستثناء Maven Wrapper.
# من الجيد تتبع هذه الملفات:
#   .mvn/wrapper/maven-wrapper.jar
#   mvnw
#   mvnw.cmd


########################################
# Generic build outputs
########################################
bin/
out/
build/
classes/
generated/
generated-sources/
generated-test-sources/
tmp/
temp/


########################################
# Test / Coverage
########################################
# (غالبًا داخل target/ لكن للإحتياط)
surefire-reports/
failsafe-reports/
test-output/
jacoco.exec
jacoco/
.coverage/
coverage/
*.lcov


########################################
# Logs & temp files
########################################
logs/
*.log
*.out
*.err
*.tmp
*.pid
*.seed
*.orig
*.rej
*.bak
*.old
*.swp
*.swo


########################################
# IDEs / Editors
########################################
# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Eclipse
.project
.classpath
.settings/

# NetBeans
nbproject/private/
build/
nbbuild/
dist/
nbdist/
.nb-gradle/

# VS Code
.vscode/
.history/


########################################
# Frontend (if any inside admin-web or others)
########################################
node_modules/
**/node_modules/
bower_components/
dist/
.cache/
.npm/
.yarn/
.pnp.*
.next/
.nuxt/
.vite/
.svelte-kit/
storybook-static/
coverage-final.json


########################################
# Secrets & local config (keep samples)
########################################
# بيئة التشغيل
*.env
*.env.*
!.env.example
!.env.sample

# مفاتيح وشهادات
*.keystore
*.jks
*.p12
*.pfx
*.pem
*.key
*.crt
*.cer
*.der
*.ks
*.truststore

# ملفات إعدادات محلية/سرية شائعة
*secret*.yml
*secret*.yaml
*secret*.properties
*local*.yml
*local*.yaml
*local*.properties


########################################
# Archives & dumps
########################################
*.tar
*.gz
*.zip
*.7z
*.rar
*.dump
*.sql
*.sqlite
*.h2.db
