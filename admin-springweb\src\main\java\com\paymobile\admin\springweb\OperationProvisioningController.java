package com.paymobile.admin.springweb;

import com.paymobile.admin.dto.OperationDefinition;
import com.paymobile.admin.service.OperationProvisioningService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
public class OperationProvisioningController {
    private final OperationProvisioningService service;

    public OperationProvisioningController(OperationProvisioningService service) {
        this.service = service;
    }

    @PostMapping("/operations/provision")
    public OperationProvisioningService.Summary provision(@RequestBody OperationDefinition def) {
        return service.provisionOperation(def);
    }
}
