package com.paymobile.admin.dao;

import com.paymobile.admin.config.ApplicationProperties;
import org.junit.Test;
import org.mockito.ArgumentCaptor;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class ParameterDaoTest {

    private static final String LASTUPDATE = "ROUND((SYSDATE - TO_DATE('1970-01-01','YYYY-MM-DD')) * 86400)";
    private static final String SQL_EXISTS_PARAMETER = "SELECT 1 FROM PARAMETER WHERE PARAMETERID = ?";
    private static final String SQL_INSERT_PARAMETER = "INSERT INTO PARAMETER (PARAMETERID, NAME, STATE, LASTUPDATE) VALUES (?, ?, 'ACTIVE', " + LASTUPDATE + ")";

    @Test
    public void testInitDao() throws Exception {
        ApplicationProperties props = ApplicationProperties.load(null);
        ParameterDao dao = new ParameterDao(props);
        assertNotNull(dao);
    }

    @Test
    public void testIdempotencyNoDuplicateInsert() throws Exception {
        ApplicationProperties props = ApplicationProperties.load(null);
        ParameterDao dao = spy(new ParameterDao(props));

        Connection c1 = mock(Connection.class);
        PreparedStatement psExists1 = mock(PreparedStatement.class);
        ResultSet rs1 = mock(ResultSet.class);
        PreparedStatement psInsert = mock(PreparedStatement.class);

        when(c1.prepareStatement(SQL_EXISTS_PARAMETER)).thenReturn(psExists1);
        when(psExists1.executeQuery()).thenReturn(rs1);
        when(rs1.next()).thenReturn(false);
        when(c1.prepareStatement(SQL_INSERT_PARAMETER)).thenReturn(psInsert);

        Connection c2 = mock(Connection.class);
        PreparedStatement psExists2 = mock(PreparedStatement.class);
        ResultSet rs2 = mock(ResultSet.class);
        when(c2.prepareStatement(SQL_EXISTS_PARAMETER)).thenReturn(psExists2);
        when(psExists2.executeQuery()).thenReturn(rs2);
        when(rs2.next()).thenReturn(true);

        // sequentially return two connections for consecutive calls
        doReturn(c1, c2).when(dao).getConnection(anyBoolean());

        dao.upsertParameter(1, "P", false);
        dao.upsertParameter(1, "P", false);

        verify(c1).prepareStatement(SQL_INSERT_PARAMETER);
        verify(psInsert).executeUpdate();
        verify(c2, never()).prepareStatement(SQL_INSERT_PARAMETER);
    }

    @Test
    public void testUpsertEnabledUpdatesExisting() throws Exception {
        ApplicationProperties props = ApplicationProperties.load(null);
        props.raw().setProperty("db.upsertEnabled", "true");
        ParameterDao dao = spy(new ParameterDao(props));

        Connection con = mock(Connection.class);
        PreparedStatement psExists = mock(PreparedStatement.class);
        ResultSet rs = mock(ResultSet.class);
        when(con.prepareStatement(SQL_EXISTS_PARAMETER)).thenReturn(psExists);
        when(psExists.executeQuery()).thenReturn(rs);
        when(rs.next()).thenReturn(true);

        String updateSql = "UPDATE PARAMETER SET NAME = ?, LASTUPDATE = " + LASTUPDATE + " WHERE PARAMETERID = ?";
        PreparedStatement psUpdate = mock(PreparedStatement.class);
        when(con.prepareStatement(updateSql)).thenReturn(psUpdate);

        doReturn(con).when(dao).getConnection(anyBoolean());

        dao.upsertParameter(2, "X", false);

        verify(con).prepareStatement(updateSql);
        verify(psUpdate).executeUpdate();
    }

    @Test
    public void testLastUpdateFormulaUsed() throws Exception {
        ApplicationProperties props = ApplicationProperties.load(null);
        ParameterDao dao = spy(new ParameterDao(props));

        Connection con = mock(Connection.class);
        PreparedStatement psExists = mock(PreparedStatement.class);
        ResultSet rs = mock(ResultSet.class);
        when(con.prepareStatement(SQL_EXISTS_PARAMETER)).thenReturn(psExists);
        when(psExists.executeQuery()).thenReturn(rs);
        when(rs.next()).thenReturn(false);

        ArgumentCaptor<String> sqlCap = ArgumentCaptor.forClass(String.class);
        PreparedStatement psInsert = mock(PreparedStatement.class);
        when(con.prepareStatement(sqlCap.capture())).thenReturn(psInsert);

        doReturn(con).when(dao).getConnection(anyBoolean());

        dao.upsertParameter(3, "Y", false);

        assertTrue(sqlCap.getAllValues().contains(SQL_INSERT_PARAMETER));
    }

    @Test
    public void testDatabaseErrorHandling() throws Exception {
        ApplicationProperties props = ApplicationProperties.load(null);
        ParameterDao dao = spy(new ParameterDao(props));

        Connection con = mock(Connection.class);
        PreparedStatement psExists = mock(PreparedStatement.class);
        ResultSet rs = mock(ResultSet.class);
        PreparedStatement psInsert = mock(PreparedStatement.class);

        when(con.prepareStatement(SQL_EXISTS_PARAMETER)).thenReturn(psExists);
        when(psExists.executeQuery()).thenReturn(rs);
        when(rs.next()).thenReturn(false);
        when(con.prepareStatement(SQL_INSERT_PARAMETER)).thenReturn(psInsert);
        doThrow(new SQLException("fail")).when(psInsert).executeUpdate();

        doReturn(con).when(dao).getConnection(anyBoolean());

        try {
            dao.upsertParameter(4, "Z", false);
            fail("Should throw");
        } catch (SQLException e) {
            verify(con).rollback();
        }
    }
}
