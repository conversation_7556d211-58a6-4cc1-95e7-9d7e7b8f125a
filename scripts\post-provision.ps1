$uri = "http://localhost:8080/admin-web/api/operations/provision"
$headers = @{
    "Accept" = "application/json"
    "Content-Type" = "application/json"
}
$body = @'
{
  "serviceCode": "EMONEY",
  "operationCode": "PAY",
  "state": "ENABLED"
}
'@

Write-Host "Testing API endpoint: $uri" -ForegroundColor Yellow
Write-Host "Request payload:" -ForegroundColor Cyan
Write-Host $body -ForegroundColor White

try {
    $response = Invoke-RestMethod -Uri $uri -Method Post -Headers $headers -Body $body -ErrorAction Stop
    
    Write-Host "`n✓ Response received:" -ForegroundColor Green
    $responseJson = $response | ConvertTo-Json -Depth 10
    Write-Host $responseJson -ForegroundColor White
    
    # Check for failures
    if ($response.failed -and $response.failed.Count -gt 0) {
        Write-Host "`n✗ API call succeeded but reported failures:" -ForegroundColor Red
        foreach ($failure in $response.failed) {
            Write-Host "  - $failure" -ForegroundColor Red
        }
        exit 1
    } else {
        Write-Host "`n✓ API call successful - no failures reported" -ForegroundColor Green
        
        if ($response.created -and $response.created.Count -gt 0) {
            Write-Host "✓ Created operations:" -ForegroundColor Green
            foreach ($created in $response.created) {
                Write-Host "  - $created" -ForegroundColor Green
            }
        }
        
        if ($response.skipped -and $response.skipped.Count -gt 0) {
            Write-Host "ℹ Skipped operations:" -ForegroundColor Yellow
            foreach ($skipped in $response.skipped) {
                Write-Host "  - $skipped" -ForegroundColor Yellow
            }
        }
    }
    
} catch {
    Write-Host "`n✗ API test FAILED: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response) {
        Write-Host "HTTP Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
        
        try {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error Response: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read error response body" -ForegroundColor Red
        }
    }
    
    exit 1
}
