package com.paymobile.admin.soap;

import com.paymobile.admin.config.ApplicationProperties;
import com.paymobile.admin.dto.I18nEntry;
import com.paymobile.admin.dto.OperationDefinition;
import com.paymobile.admin.dto.Requester;
import com.paymobile.admin.dto.SofDefinition;
import com.paymobile.admin.util.LogUtil;
import com.paymobile.admin.soap.generated.operations.AddOperationAuthorizedSoFRequest;
import com.paymobile.admin.soap.generated.operations.AddOperationRequest;
import com.paymobile.admin.soap.generated.operations.AddOperationRoleRequest;
import com.paymobile.admin.soap.generated.operations.I18NEntry;
import com.paymobile.admin.soap.generated.operations.OperationsPortType;
import com.paymobile.admin.soap.generated.operations.OperationsService;
import org.jboss.logging.Logger;

import javax.xml.ws.BindingProvider;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SOAP client for Operations service (JAX-WS)
 * Uses wsimport-generated interface if present; here we demonstrate dynamic proxy pattern
 * but the project is configured to generate stubs from WSDL at build time.
 */
public class OperationsSoapClient {
    private static final Logger log = Logger.getLogger(OperationsSoapClient.class);

    private final ApplicationProperties props;
    private final URL wsdlUrl;
    private final String endpointOverride;
    private final int connectTimeout;
    private final int readTimeout;
    private final boolean logPayloads;

    public OperationsSoapClient(ApplicationProperties props) throws Exception {
        this.props = props;

        // Handle WSDL URL with better error handling and fallbacks
        String wsdlUrlString = props.get("operations.wsdlUrl", "http://wsdler-test.tamkeen.com.ye:8080/businessservices/operations/?wsdl");

        try {
            // Try to create URL and validate it's accessible
            this.wsdlUrl = new URL(wsdlUrlString);
            log.infof("Configured WSDL URL: %s", wsdlUrlString);

            // Test WSDL accessibility if it's an HTTP URL
            if (wsdlUrlString.startsWith("http")) {
                try {
                    java.net.URLConnection conn = this.wsdlUrl.openConnection();
                    conn.setConnectTimeout(5000);
                    conn.setReadTimeout(10000);
                    conn.connect();
                    log.infof("WSDL URL is accessible: %s", wsdlUrlString);
                } catch (Exception e) {
                    log.warnf("WSDL URL may not be accessible: %s (Error: %s)", wsdlUrlString, e.getMessage());
                    // Continue anyway - service might be available at runtime
                }
            }

        } catch (Exception e) {
            log.errorf("Invalid WSDL URL: %s (Error: %s)", wsdlUrlString, e.getMessage());
            throw new Exception("Failed to configure SOAP client with WSDL URL: " + wsdlUrlString, e);
        }

        this.endpointOverride = props.get("operations.endpointUrl", "");
        this.connectTimeout = props.getInt("operations.connectTimeoutMs", 15000);
        this.readTimeout = props.getInt("operations.readTimeoutMs", 30000);
        this.logPayloads = props.getBoolean("log.soapPayloads", false);

        log.infof("SOAP client configured - WSDL: %s, Endpoint Override: %s, Timeouts: %d/%d ms",
                 wsdlUrlString, endpointOverride.isEmpty() ? "NONE" : endpointOverride, connectTimeout, readTimeout);
    }

    private <T> T configurePort(T port) {
        Map<String, Object> ctx = ((BindingProvider) port).getRequestContext();
        if (endpointOverride != null && endpointOverride.trim().length() > 0) {
            ctx.put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, endpointOverride);
        }
        ctx.put("com.sun.xml.internal.ws.connect.timeout", connectTimeout);
        ctx.put("com.sun.xml.internal.ws.request.timeout", readTimeout);
        ctx.put("com.sun.xml.ws.connect.timeout", connectTimeout);
        ctx.put("com.sun.xml.ws.request.timeout", readTimeout);
        return port;
    }

    public void addOperation(Requester requester, OperationDefinition def) {
        if (logPayloads) {
            Map<String, Object> payload = new HashMap<String, Object>();
            payload.put("requester", requester);
            payload.put("serviceCode", def.getServiceCode());
            payload.put("operationCode", def.getOperationCode());
            payload.put("state", def.getState());
            payload.put("comment", def.getComment());
            payload.put("i18n", def.getI18n());
            log.debugf("SOAP addOperation payload: %s", LogUtil.safe(String.valueOf(payload)));
        }

        try {
            log.debugf("Creating OperationsService with WSDL URL: %s", wsdlUrl);
            OperationsService svc = new OperationsService(wsdlUrl);
            OperationsPortType port = configurePort(svc.getOperationsPort());
            log.debugf("Successfully created SOAP port for addOperation");

            AddOperationRequest req = new AddOperationRequest();
            req.setRequester(toSoapRequester(requester));
            req.setServiceCode(def.getServiceCode());
            req.setOperationCode(def.getOperationCode());
            req.setState(def.getState());
            req.setComment(def.getComment());
            List<I18nEntry> i18n = def.getI18n();
            if (i18n != null) {
                for (I18nEntry e : i18n) {
                    I18NEntry ge = new I18NEntry();
                    ge.setLanguageCode(e.getLanguageCode());
                    ge.setValue(e.getValue());
                    req.getI18N().add(ge);
                }
            }
            port.addOperation(req);
            log.debugf("Successfully called addOperation SOAP service");

        } catch (Exception e) {
            log.errorf("Failed to call addOperation SOAP service: %s", e.getMessage());
            throw new RuntimeException("SOAP addOperation failed", e);
        }
    }

    public void addOperationRole(Requester requester, String serviceCode, String operationCode, String roleCode, String comment) {
        if (logPayloads) {
            Map<String, Object> payload = new HashMap<String, Object>();
            payload.put("requester", requester);
            payload.put("serviceCode", serviceCode);
            payload.put("operationCode", operationCode);
            payload.put("roleCode", roleCode);
            payload.put("comment", comment);
            log.debugf("SOAP addOperationRole payload: %s", LogUtil.safe(String.valueOf(payload)));
        }

        try {
            log.debugf("Creating OperationsService for addOperationRole with WSDL URL: %s", wsdlUrl);
            OperationsService svc = new OperationsService(wsdlUrl);
            OperationsPortType port = configurePort(svc.getOperationsPort());
            AddOperationRoleRequest req = new AddOperationRoleRequest();
            req.setRequester(toSoapRequester(requester));
            req.setServiceCode(serviceCode);
            req.setOperationCode(operationCode);
            req.setRoleCode(roleCode);
            req.setComment(comment);
            port.addOperationRole(req);
            log.debugf("Successfully called addOperationRole SOAP service");

        } catch (Exception e) {
            log.errorf("Failed to call addOperationRole SOAP service: %s", e.getMessage());
            throw new RuntimeException("SOAP addOperationRole failed", e);
        }
    }

    public void addOperationAuthorizedSoF(Requester requester, OperationDefinition def) {
        if (logPayloads) {
            Map<String, Object> payload = new HashMap<String, Object>();
            payload.put("requester", requester);
            payload.put("serviceType", def.getServiceCode());
            payload.put("operationType", def.getOperationCode());
            payload.put("authorizedSoFList", def.getAuthorizedSoFList());
            log.debugf("SOAP addOperationAuthorizedSoF payload: %s", LogUtil.safe(String.valueOf(payload)));
        }

        try {
            log.debugf("Creating OperationsService for addOperationAuthorizedSoF with WSDL URL: %s", wsdlUrl);
            OperationsService svc = new OperationsService(wsdlUrl);
            OperationsPortType port = configurePort(svc.getOperationsPort());
            AddOperationAuthorizedSoFRequest req = new AddOperationAuthorizedSoFRequest();
            req.setRequester(toSoapRequester(requester));
            req.setServiceType(def.getServiceCode());
            req.setOperationType(def.getOperationCode());
            if (def.getAuthorizedSoFList() != null) {
                for (SofDefinition sof : def.getAuthorizedSoFList()) {
                    String val = sof.getCategory();
                    if (sof.getSubcategory() != null && sof.getSubcategory().length() > 0) {
                        val += ":" + sof.getSubcategory();
                    }
                    req.getAuthorizedSoFList().add(val);
                }
            }
            port.addOperationAuthorizedSoF(req);
            log.debugf("Successfully called addOperationAuthorizedSoF SOAP service");

        } catch (Exception e) {
            log.errorf("Failed to call addOperationAuthorizedSoF SOAP service: %s", e.getMessage());
            throw new RuntimeException("SOAP addOperationAuthorizedSoF failed", e);
        }
    }

    private com.paymobile.admin.soap.generated.operations.Requester toSoapRequester(Requester r) {
        com.paymobile.admin.soap.generated.operations.Requester s = new com.paymobile.admin.soap.generated.operations.Requester();
        if (r != null) {
            s.setAccessMedium(r.getAccessMedium());
            s.setDomainId(r.getDomainId());
            s.setAccessType(r.getAccessType());
            s.setAccessValue(r.getAccessValue());
            s.setExternalSessionId(r.getExternalSessionId());
            s.setPassword(r.getPassword());
        }
        return s;
    }
}
