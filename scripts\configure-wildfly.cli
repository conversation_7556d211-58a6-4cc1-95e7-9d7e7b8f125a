# WildFly CL<PERSON> script to configure PayMobile Operation Admin
# Usage: $WILDFLY_HOME/bin/jboss-cli.sh --connect --file=configure-wildfly.cli

# Set system property for application properties file location
/system-property=paymobile.admin.props:add(value="${jboss.server.config.dir}/application.properties")

# Configure SOAP client timeouts
/system-property=com.sun.xml.ws.connect.timeout:add(value="15000")
/system-property=com.sun.xml.ws.request.timeout:add(value="30000")

# Enable JAX-WS debugging (optional)
/system-property=com.sun.xml.ws.transport.http.client.HttpTransportPipe.dump:add(value="true")

# Configure logging for better debugging
/subsystem=logging/logger=com.paymobile.admin:add(level=INFO)
/subsystem=logging/logger=org.apache.cxf:add(level=WARN)

# Reload configuration
reload

echo "WildFly configured for PayMobile Operation Admin"
echo "Make sure to:"
echo "1. Copy scripts/wildfly-application.properties to $WILDFLY_HOME/standalone/configuration/application.properties"
echo "2. Update SOAP service URLs in application.properties"
echo "3. Configure PayMobileDS datasource"
