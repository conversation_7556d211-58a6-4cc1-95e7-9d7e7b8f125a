package com.paymobile.admin.service;

import com.paymobile.admin.config.ApplicationProperties;
import com.paymobile.admin.dao.ParameterDao;
import com.paymobile.admin.dto.OperationDefinition;
import com.paymobile.admin.dto.ParameterEntry;
import com.paymobile.admin.dto.ParameterPropertyEntry;
import com.paymobile.admin.dto.Requester;
import com.paymobile.admin.soap.OperationsSoapClient;
import org.jboss.logging.Logger;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;

public class OperationProvisioningService {
    private static final Logger log = Logger.getLogger(OperationProvisioningService.class);

    private final ApplicationProperties props;
    private final OperationsSoapClient soapClient;
    private final ParameterDao parameterDao;
    private final boolean serverMode;

    @Schema(description = "Result summary of a provisioning run")
    public static class Summary {
        @Schema(description = "Items that were created")
        public List<String> created = new ArrayList<String>();
        @Schema(description = "Items that were skipped")
        public List<String> skipped = new ArrayList<String>();
        @Schema(description = "Items that failed")
        public List<String> failed = new ArrayList<String>();
    }

    public OperationProvisioningService(ApplicationProperties props) throws Exception {
        this(props, new OperationsSoapClient(props), new ParameterDao(props), false);
    }

    public OperationProvisioningService(ApplicationProperties props,
                                        OperationsSoapClient soapClient,
                                        ParameterDao parameterDao,
                                        boolean serverMode) {
        this.props = props;
        this.soapClient = soapClient;
        this.parameterDao = parameterDao;
        this.serverMode = serverMode;
    }

    public Summary provisionOperation(OperationDefinition def) {
        Summary summary = new Summary();
        try {
            // 1. addOperation
            Requester webRequester = buildRequester("WEB", null);
            soapClient.addOperation(webRequester, def);
            summary.created.add("addOperation:" + def.getOperationCode());

            // 2. addOperationRole (twice)
            soapClient.addOperationRole(webRequester, def.getServiceCode(), def.getOperationCode(), "REQUESTER", "Requester role");
            soapClient.addOperationRole(webRequester, def.getServiceCode(), def.getOperationCode(), "SENDER", "Sender role");
            summary.created.add("addOperationRole:REQUESTER");
            summary.created.add("addOperationRole:SENDER");

            // 3. addOperationAuthorizedSoF (SOAP requester with password)
            Requester soapRequester = buildRequester("SOAP", props.get("requester.password", ""));
            soapClient.addOperationAuthorizedSoF(soapRequester, def);
            summary.created.add("addOperationAuthorizedSoF");

            // 4. Database writes in single transaction style per call (here managed per DAO method)
            if (def.getParameterEntries() != null) {
                for (ParameterEntry pe : def.getParameterEntries()) {
                    parameterDao.upsertParameter(pe.getParameterId(), pe.getName(), this.serverMode);
                    summary.created.add("PARAMETER:" + pe.getParameterId());
                }
            }
            if (def.getParameterPropertyEntries() != null) {
                for (ParameterPropertyEntry ppe : def.getParameterPropertyEntries()) {
                    parameterDao.upsertParameterProperty(ppe.getParameterId(), ppe.getName(), ppe.getValueString(), this.serverMode);
                    summary.created.add("PARAMETERPROPERTY:" + ppe.getParameterId() + ":" + ppe.getName());
                }
            }
        } catch (Exception e) {
            log.error("Provisioning failed", e);
            summary.failed.add(e.getMessage());
            if (!props.getBoolean("continueOnError", false)) {
                return summary;
            }
        }
        return summary;
    }

    public boolean existsOperation(String serviceCode, String operationCode) {
        // TODO: Implement SOAP query or DB heuristic consistent with PayMobile
        return false;
    }

    private Requester buildRequester(String medium, String password) {
        Requester r = new Requester();
        r.setAccessMedium(medium);
        r.setDomainId(Integer.parseInt(props.get("requester.domainId", "0")));
        r.setAccessType(props.get("requester.accessType", "USERNAME"));
        r.setAccessValue(props.get("requester.accessValue", "ESG_ADMIN"));
        r.setExternalSessionId(props.get("requester.externalSessionId", ""));
        r.setPassword(password);
        return r;
    }
}
