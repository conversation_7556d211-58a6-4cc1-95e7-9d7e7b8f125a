# PayMobile Operation Admin - Fix Validation Script
# This script validates that the ServiceConstructionException fix is working

param(
    [string]$WsdlUrl = "http://wsdler-test.tamkeen.com.ye:8080/businessservices/operations/?wsdl"
)

Write-Host "=== PayMobile Operation Admin - Fix Validation ===" -ForegroundColor Cyan
Write-Host ""

$allTestsPassed = $true

# Test 1: WSDL Accessibility
Write-Host "1. Testing WSDL accessibility..." -ForegroundColor Yellow
try {
    & ".\scripts\test-wsdl.ps1" -WsdlUrl $WsdlUrl
    Write-Host "✓ WSDL test PASSED" -ForegroundColor Green
} catch {
    Write-Host "✗ WSDL test FAILED" -ForegroundColor Red
    $allTestsPassed = $false
}

# Test 2: WildFly Configuration
Write-Host "`n2. Checking WildFly configuration..." -ForegroundColor Yellow
$configFile = "$env:WILDFLY_HOME\standalone\configuration\application.properties"
if (Test-Path $configFile) {
    Write-Host "✓ Configuration file exists" -ForegroundColor Green
    
    $content = Get-Content $configFile -Raw
    if ($content -match "operations\.wsdlUrl=(.+)") {
        $configuredWsdl = $matches[1].Trim()
        Write-Host "✓ WSDL URL configured: $configuredWsdl" -ForegroundColor Green
        
        if ($configuredWsdl -ne "http://localhost:8080/operations?wsdl") {
            Write-Host "✓ WSDL URL is not using problematic default" -ForegroundColor Green
        } else {
            Write-Host "⚠ WSDL URL is still using problematic default" -ForegroundColor Yellow
            $allTestsPassed = $false
        }
    } else {
        Write-Host "✗ WSDL URL not configured" -ForegroundColor Red
        $allTestsPassed = $false
    }
} else {
    Write-Host "✗ Configuration file missing" -ForegroundColor Red
    $allTestsPassed = $false
}

# Test 3: API Endpoint
Write-Host "`n3. Testing API endpoint..." -ForegroundColor Yellow
try {
    & ".\scripts\post-provision.ps1"
    Write-Host "✓ API test PASSED" -ForegroundColor Green
} catch {
    Write-Host "✗ API test FAILED" -ForegroundColor Red
    $allTestsPassed = $false
}

# Test 4: Check for ServiceConstructionException in logs
Write-Host "`n4. Checking WildFly logs for errors..." -ForegroundColor Yellow
$logFile = "$env:WILDFLY_HOME\standalone\log\server.log"
if (Test-Path $logFile) {
    $recentLogs = Get-Content $logFile -Tail 100
    $serviceErrors = $recentLogs | Where-Object { $_ -match "ServiceConstructionException" }
    
    if ($serviceErrors.Count -eq 0) {
        Write-Host "✓ No ServiceConstructionException found in recent logs" -ForegroundColor Green
    } else {
        Write-Host "✗ ServiceConstructionException still present in logs:" -ForegroundColor Red
        $serviceErrors | ForEach-Object { Write-Host "  $_" -ForegroundColor Red }
        $allTestsPassed = $false
    }
} else {
    Write-Host "⚠ WildFly log file not found at: $logFile" -ForegroundColor Yellow
}

# Final Result
Write-Host "`n=== Validation Results ===" -ForegroundColor Cyan
if ($allTestsPassed) {
    Write-Host "✅ ALL TESTS PASSED - Fix is working correctly!" -ForegroundColor Green
    Write-Host ""
    Write-Host "The ServiceConstructionException issue has been resolved." -ForegroundColor Green
    Write-Host "You can now successfully call the provision API." -ForegroundColor Green
    exit 0
} else {
    Write-Host "❌ SOME TESTS FAILED - Fix needs attention" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please review the failed tests above and:" -ForegroundColor Yellow
    Write-Host "1. Ensure WildFly is properly configured" -ForegroundColor White
    Write-Host "2. Check SOAP service availability" -ForegroundColor White
    Write-Host "3. Review WildFly logs for detailed errors" -ForegroundColor White
    exit 1
}
