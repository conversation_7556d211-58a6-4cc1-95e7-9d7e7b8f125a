package com.paymobile.admin.config;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Properties;

/**
 * Loads application properties from a file, with environment variable overrides.
 * All properties can be overridden via System properties or environment variables
 * by using upper-case with dots replaced by underscores.
 */
public class ApplicationProperties {
    private final Properties props = new Properties();

    public static ApplicationProperties load(String path) {
        ApplicationProperties ap = new ApplicationProperties();
        ap.loadFrom(path);
        return ap;
    }

    private void loadFrom(String path) {
        try {
            if (path != null && !path.trim().isEmpty()) {
                try (InputStream in = new FileInputStream(path)) {
                    props.load(in);
                }
            }
        } catch (Exception ignored) {}
        // System properties override
        for (String name : System.getProperties().stringPropertyNames()) {
            props.setProperty(name, System.getProperty(name));
        }
        // Environment overrides (convert NAME like operations_wsdlUrl or OPERATIONS_WSDLURL)
        for (String name : props.stringPropertyNames()) {
            String envName = name.replace('.', '_').toUpperCase();
            String v = System.getenv(envName);
            if (v != null) props.setProperty(name, v);
        }
    }

    public String get(String key, String def) {
        String v = props.getProperty(key);
        return v != null && !v.isEmpty() ? v : def;
    }

    public boolean getBoolean(String key, boolean def) {
        String v = props.getProperty(key);
        return v != null ? Boolean.parseBoolean(v) : def;
    }

    public int getInt(String key, int def) {
        String v = props.getProperty(key);
        try { return v != null ? Integer.parseInt(v) : def; } catch (Exception e) { return def; }
    }

    public Properties raw() {
        return props;
    }
}
