@echo off
set URI=http://localhost:8080/admin-web/api/operations/provision

echo Testing API with curl...
echo URI: %URI%
echo.

curl.exe -X POST "%URI%" ^
  -H "Accept: application/json" ^
  -H "Content-Type: application/json" ^
  --data-binary @scripts/payload.json ^
  --fail-with-body ^
  --show-error ^
  --write-out "%%{http_code} %%{time_total}s\n"

if %ERRORLEVEL% neq 0 (
    echo.
    echo ✗ API test failed with exit code %ERRORLEVEL%
    exit /b 1
)

echo.
echo ✓ API test completed successfully
