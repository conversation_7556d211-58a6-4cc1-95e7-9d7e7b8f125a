package com.paymobile.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "Parameter definition")
public class ParameterEntry implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "Unique parameter identifier")
    private int parameterId;
    @Schema(description = "Parameter name")
    private String name;

    public int getParameterId() { return parameterId; }
    public void setParameterId(int parameterId) { this.parameterId = parameterId; }
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
}
