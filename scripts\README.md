# PayMobile Operation Admin - Scripts Directory

This directory contains scripts for diagnosing, fixing, and validating the ServiceConstructionException issue in the PayMobile Operation Admin application.

## Quick Start

### 1. Diagnose the Issue
```powershell
.\scripts\diagnose-issue.ps1
```

### 2. Configure WildFly
```bash
# Run WildFly CLI script
$WILDFLY_HOME/bin/jboss-cli.sh --connect --file=scripts/configure-wildfly.cli

# Copy configuration file
cp scripts/wildfly-application.properties $WILDFLY_HOME/standalone/configuration/application.properties
```

### 3. Update SOAP Service URLs
Edit `$WILDFLY_HOME/standalone/configuration/application.properties` and set:
```properties
operations.wsdlUrl=http://your-soap-service/operations/?wsdl
operations.endpointUrl=http://your-soap-service/operations/
requester.password=YOUR_ACTUAL_PASSWORD
```

### 4. Validate the Fix
```powershell
.\scripts\validate-fix.ps1 -WsdlUrl "http://your-soap-service/operations/?wsdl"
```

## Script Descriptions

### Diagnosis Scripts
- **`diagnose-issue.ps1`** - Comprehensive diagnosis of the current issue
- **`test-wsdl.ps1`** - Tests WSDL URL accessibility and validates content

### Configuration Scripts
- **`configure-wildfly.cli`** - WildFly CLI script to set system properties
- **`wildfly-application.properties`** - Correct configuration template

### Testing Scripts
- **`post-provision.ps1`** - Tests the provision API endpoint
- **`post-provision.curl.bat`** - Alternative curl-based API test
- **`payload.json`** - Test payload for API calls

### Logging Scripts
- **`enable-cxf-debug.cli`** - Enables DEBUG logging for CXF components
- **`disable-cxf-debug.cli`** - Disables DEBUG logging

### Validation Scripts
- **`validate-fix.ps1`** - Comprehensive validation that the fix is working

## Usage Examples

### Test WSDL Accessibility
```powershell
# Test external SOAP service
.\scripts\test-wsdl.ps1 -WsdlUrl "http://wsdler-test.tamkeen.com.ye:8080/businessservices/operations/?wsdl"

# Test local SOAP service
.\scripts\test-wsdl.ps1 -WsdlUrl "http://localhost:8080/businessservices/operations/?wsdl"
```

### Test API Endpoint
```powershell
# PowerShell version
.\scripts\post-provision.ps1

# Batch/curl version
.\scripts\post-provision.curl.bat
```

### Enable Debug Logging
```bash
$WILDFLY_HOME/bin/jboss-cli.sh --connect --file=scripts/enable-cxf-debug.cli
```

## Troubleshooting

### Common Issues

1. **WSDL Not Accessible**
   - Check if SOAP service is running
   - Verify network connectivity
   - Update WSDL URL in configuration

2. **WildFly Configuration Missing**
   - Run `configure-wildfly.cli`
   - Copy `wildfly-application.properties` to config directory
   - Restart WildFly

3. **API Returns ServiceConstructionException**
   - Check WildFly logs for detailed errors
   - Enable debug logging with `enable-cxf-debug.cli`
   - Validate WSDL content and service names

### Log Locations
- **WildFly Logs**: `$WILDFLY_HOME/standalone/log/server.log`
- **Configuration**: `$WILDFLY_HOME/standalone/configuration/application.properties`

## Expected Results

### Successful API Response
```json
{
  "created": ["addOperation:PAY", "addOperationRole:PAY", "addOperationAuthorizedSoF"],
  "skipped": [],
  "failed": []
}
```

### Failed Response (Before Fix)
```json
{
  "created": [],
  "skipped": [],
  "failed": ["org.apache.cxf.service.factory.ServiceConstructionException: Failed to create service."]
}
```

## Prerequisites

- WildFly 15.0.1.Final running
- PowerShell (for .ps1 scripts)
- curl.exe (for .bat scripts)
- admin-web.war deployed to WildFly
- Network access to SOAP services
