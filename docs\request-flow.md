# Request Flow

This document illustrates how a provisioning request travels from the client through the service layers and down to the database.

```mermaid
sequenceDiagram
    participant C as Client
    participant API as REST API
    participant S as OperationProvisioningService
    participant SOAP as Operations SOAP Service
    participant DAO as ParameterDao
    participant DB as PayMobile Database

    C->>API: POST /api/operations/provision
    API->>S: provisionOperation(def)
    S->>SOAP: addOperation
    SOAP-->>S: response
    S->>SOAP: addOperationRole x2
    SOAP-->>S: responses
    S->>SOAP: addOperationAuthorizedSoF
    SOAP-->>S: response
    S->>DAO: upsertParameter entries
    DAO->>DB: INSERT/UPDATE PARAMETER
    DB-->>DAO: result
    S->>DAO: upsertParameterProperty entries
    DAO->>DB: INSERT/UPDATE PARAMETERPROPERTY
    DB-->>DAO: result
    DAO-->>S: status
    S-->>API: Summary
    API-->>C: Provisioning summary
```

1. **Client** sends a `POST /api/operations/provision` request with an operation definition.
2. **REST API** controller delegates to `OperationProvisioningService`.
3. **OperationProvisioningService** orchestrates the process by invoking the remote **Operations SOAP Service** to register the operation and its roles.
4. The service then uses **ParameterDao** to insert or update `PARAMETER` and `PARAMETERPROPERTY` rows in the **PayMobile Database**.
5. A summary of created, skipped, and failed items is returned to the client.

