package com.paymobile.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "Source of funds definition")
public class SofDefinition implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "Category code", example = "BANK_ACCOUNT")
    private String category;
    @Schema(description = "Subcategory code")
    private String subcategory;
    @Schema(description = "Currency identifier", example = "840")
    private int currencyId;
    @Schema(description = "Provider code")
    private String provider;
    @Schema(description = "Whether debit is allowed", example = "YES")
    private String debitAllowed;  // YES/NO
    @Schema(description = "Whether credit is allowed", example = "NO")
    private String creditAllowed; // YES/NO

    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    public String getSubcategory() { return subcategory; }
    public void setSubcategory(String subcategory) { this.subcategory = subcategory; }
    public int getCurrencyId() { return currencyId; }
    public void setCurrencyId(int currencyId) { this.currencyId = currencyId; }
    public String getProvider() { return provider; }
    public void setProvider(String provider) { this.provider = provider; }
    public String getDebitAllowed() { return debitAllowed; }
    public void setDebitAllowed(String debitAllowed) { this.debitAllowed = debitAllowed; }
    public String getCreditAllowed() { return creditAllowed; }
    public void setCreditAllowed(String creditAllowed) { this.creditAllowed = creditAllowed; }
}
