# SOAP Configuration
operations.wsdlUrl=http://localhost:8080/operations?wsdl
operations.endpointUrl=
operations.connectTimeoutMs=15000
operations.readTimeoutMs=30000

# Default Requester Configuration
requester.accessMedium=WEB
requester.domainId=0
requester.accessType=USERNAME
requester.accessValue=ESG_ADMIN
requester.password=CHANGE_ME
requester.externalSessionId=${RANDOM_UUID}

# Database Configuration (CLI Mode)
db.url=*******************************************
db.username=PAYMOBILE4
db.password=CHANGE_ME
db.driver=oracle.jdbc.OracleDriver
db.upsertEnabled=false

# Database Configuration (Server Mode)
db.jndiName=java:/PayMobileDS

# Application Behavior
continueOnError=false
log.soapPayloads=false
