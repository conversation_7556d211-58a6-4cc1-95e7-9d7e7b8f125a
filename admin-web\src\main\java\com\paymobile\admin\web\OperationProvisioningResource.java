package com.paymobile.admin.web;

import com.paymobile.admin.config.ApplicationProperties;
import com.paymobile.admin.dto.OperationDefinition;
import com.paymobile.admin.service.OperationProvisioningService;
import com.paymobile.admin.dao.ParameterDao;
import com.paymobile.admin.soap.OperationsSoapClient;
import org.jboss.logging.Logger;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@Path("/operations")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Operations")
public class OperationProvisioningResource {
    private static final Logger log = Logger.getLogger(OperationProvisioningResource.class);

    @POST
    @Path("/provision")
    @Operation(summary = "Provision a new operation",
        requestBody = @RequestBody(required = true,
            content = @Content(schema = @Schema(implementation = OperationDefinition.class),
                examples = @ExampleObject(name = "operation",
                    value = "{\n  \"serviceCode\": \"EMONEY\",\n  \"operationCode\": \"PAY\",\n  \"state\": \"ENABLED\"\n}"))),
        responses = {
            @ApiResponse(responseCode = "200", description = "Provisioning summary",
                content = @Content(schema = @Schema(implementation = OperationProvisioningService.Summary.class),
                    examples = @ExampleObject(value = "{\n  \"created\": [\"addOperation:PAY\"],\n  \"skipped\": [],\n  \"failed\": []\n}")))
        })
    public Response provision(OperationDefinition def) {
        try {
            ApplicationProperties props = ApplicationProperties.load(System.getProperty("paymobile.admin.props"));
            OperationProvisioningService service = new OperationProvisioningService(
                    props,
                    new OperationsSoapClient(props),
                    new ParameterDao(props),
                    true);
            OperationProvisioningService.Summary summary = service.provisionOperation(def);
            return Response.ok(summary).build();
        } catch (Exception e) {
            log.error("Provisioning failed", e);
            return Response.serverError().entity("{\"error\":\"" + e.getMessage() + "\"}").build();
        }
    }
}
