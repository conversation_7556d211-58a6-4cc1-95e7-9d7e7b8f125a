package com.paymobile.admin.service;

import com.paymobile.admin.config.ApplicationProperties;
import com.paymobile.admin.dao.ParameterDao;
import com.paymobile.admin.dto.OperationDefinition;
import com.paymobile.admin.dto.Requester;
import com.paymobile.admin.soap.OperationsSoapClient;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InOrder;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class OperationProvisioningServiceTest {

    @Test
    public void testServiceInit() throws Exception {
        ApplicationProperties props = ApplicationProperties.load(null);
        OperationProvisioningService s = new OperationProvisioningService(props);
        assertNotNull(s);
    }

    @Test
    public void testProvisioningInvokesSoapClient() throws Exception {
        ApplicationProperties props = ApplicationProperties.load(null);
        props.raw().setProperty("requester.password", "secret");
        OperationsSoapClient soap = mock(OperationsSoapClient.class);
        ParameterDao dao = mock(ParameterDao.class);

        OperationProvisioningService svc = new OperationProvisioningService(props, soap, dao, false);
        OperationDefinition def = new OperationDefinition();
        def.setServiceCode("SVC");
        def.setOperationCode("OP");
        def.setState("ACTIVE");

        svc.provisionOperation(def);

        ArgumentCaptor<Requester> req1 = ArgumentCaptor.forClass(Requester.class);
        verify(soap).addOperation(req1.capture(), eq(def));
        assertEquals("WEB", req1.getValue().getAccessMedium());

        verify(soap).addOperationRole(any(Requester.class), eq("SVC"), eq("OP"), eq("REQUESTER"), eq("Requester role"));
        verify(soap).addOperationRole(any(Requester.class), eq("SVC"), eq("OP"), eq("SENDER"), eq("Sender role"));

        ArgumentCaptor<Requester> req2 = ArgumentCaptor.forClass(Requester.class);
        verify(soap).addOperationAuthorizedSoF(req2.capture(), eq(def));
        assertEquals("SOAP", req2.getValue().getAccessMedium());
        assertEquals("secret", req2.getValue().getPassword());
    }

    @Test
    public void testProvisioningStopsOnError() throws Exception {
        ApplicationProperties props = ApplicationProperties.load(null);
        props.raw().setProperty("requester.password", "secret");
        OperationsSoapClient soap = mock(OperationsSoapClient.class);
        ParameterDao dao = mock(ParameterDao.class);
        doThrow(new RuntimeException("boom")).when(soap)
            .addOperationRole(any(Requester.class), anyString(), anyString(), eq("SENDER"), anyString());

        OperationProvisioningService svc = new OperationProvisioningService(props, soap, dao, false);
        OperationDefinition def = new OperationDefinition();
        def.setServiceCode("SVC");
        def.setOperationCode("OP");

        svc.provisionOperation(def);

        InOrder in = inOrder(soap);
        in.verify(soap).addOperation(any(Requester.class), eq(def));
        in.verify(soap).addOperationRole(any(Requester.class), eq("SVC"), eq("OP"), eq("REQUESTER"), anyString());
        in.verify(soap).addOperationRole(any(Requester.class), eq("SVC"), eq("OP"), eq("SENDER"), anyString());
        verify(soap, never()).addOperationAuthorizedSoF(any(Requester.class), eq(def));
        verifyNoInteractions(dao);
    }
}
