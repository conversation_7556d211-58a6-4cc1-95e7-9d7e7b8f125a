<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>PayMobile Operation Admin API</title>
  <link rel="stylesheet" type="text/css" href="../webjars/swagger-ui/4.15.5/swagger-ui.css" />
</head>
<body>
<div id="swagger-ui"></div>

<script src="../webjars/swagger-ui/4.15.5/swagger-ui-bundle.js"></script>
<script src="../webjars/swagger-ui/4.15.5/swagger-ui-standalone-preset.js"></script>
<script>
  // يعمل مع JDK 8/WildFly 15: يحدد المسار الأساسي ديناميكياً
  function buildSpecUrl() {
    // مثال المسار: /admin-web/swagger-ui/index.html  → نرجع /admin-web
    var base = window.location.pathname.replace(/\/swagger-ui\/.*/, '');
    // لو ApplicationPath لديك هو /api (وهو المرجح من اللوج)
    return base + '/api/openapi.json';
  }

  window.onload = function () {
    var specUrl = buildSpecUrl();
    window.ui = SwaggerUIBundle({
      url: specUrl,                 // مثال ناتج: /admin-web/api/openapi.json
      dom_id: '#swagger-ui',
      presets: [SwaggerUIBundle.presets.apis, SwaggerUIStandalonePreset],
      layout: 'BaseLayout'
    });
  };
</script>
</body>
</html>
