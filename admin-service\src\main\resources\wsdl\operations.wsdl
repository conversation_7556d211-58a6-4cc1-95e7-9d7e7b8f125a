<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:tns="http://paymobile.eservglobal.com/businessservices/operations"
                  targetNamespace="http://paymobile.eservglobal.com/businessservices/operations">
  <wsdl:types>
    <xsd:schema targetNamespace="http://paymobile.eservglobal.com/businessservices/operations" elementFormDefault="qualified">
      <xsd:complexType name="I18nEntry">
        <xsd:sequence>
          <xsd:element name="languageCode" type="xsd:string"/>
          <xsd:element name="value" type="xsd:string"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="Requester">
        <xsd:sequence>
          <xsd:element name="accessMedium" type="xsd:string"/>
          <xsd:element name="domainId" type="xsd:int"/>
          <xsd:element name="accessType" type="xsd:string"/>
          <xsd:element name="accessValue" type="xsd:string"/>
          <xsd:element name="externalSessionId" type="xsd:string" minOccurs="0"/>
          <xsd:element name="password" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:element name="addOperationRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="requester" type="tns:Requester"/>
            <xsd:element name="serviceCode" type="xsd:string"/>
            <xsd:element name="operationCode" type="xsd:string"/>
            <xsd:element name="state" type="xsd:string"/>
            <xsd:element name="comment" type="xsd:string" minOccurs="0"/>
            <xsd:element name="i18n" type="tns:I18nEntry" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="addOperationResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="resultCode" type="xsd:string"/>
            <xsd:element name="resultMessage" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="addOperationRoleRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="requester" type="tns:Requester"/>
            <xsd:element name="serviceCode" type="xsd:string"/>
            <xsd:element name="operationCode" type="xsd:string"/>
            <xsd:element name="roleCode" type="xsd:string"/>
            <xsd:element name="comment" type="xsd:string" minOccurs="0"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="addOperationRoleResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="resultCode" type="xsd:string"/>
            <xsd:element name="resultMessage" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="addOperationAuthorizedSoFRequest">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="requester" type="tns:Requester"/>
            <xsd:element name="serviceType" type="xsd:string"/>
            <xsd:element name="operationType" type="xsd:string"/>
            <xsd:element name="authorizedSoFList" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="addOperationAuthorizedSoFResponse">
        <xsd:complexType>
          <xsd:sequence>
            <xsd:element name="resultCode" type="xsd:string"/>
            <xsd:element name="resultMessage" type="xsd:string"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
    </xsd:schema>
  </wsdl:types>

  <wsdl:message name="addOperationRequest">
    <wsdl:part name="parameters" element="tns:addOperationRequest"/>
  </wsdl:message>
  <wsdl:message name="addOperationResponse">
    <wsdl:part name="parameters" element="tns:addOperationResponse"/>
  </wsdl:message>

  <wsdl:message name="addOperationRoleRequest">
    <wsdl:part name="parameters" element="tns:addOperationRoleRequest"/>
  </wsdl:message>
  <wsdl:message name="addOperationRoleResponse">
    <wsdl:part name="parameters" element="tns:addOperationRoleResponse"/>
  </wsdl:message>

  <wsdl:message name="addOperationAuthorizedSoFRequest">
    <wsdl:part name="parameters" element="tns:addOperationAuthorizedSoFRequest"/>
  </wsdl:message>
  <wsdl:message name="addOperationAuthorizedSoFResponse">
    <wsdl:part name="parameters" element="tns:addOperationAuthorizedSoFResponse"/>
  </wsdl:message>

  <wsdl:portType name="OperationsPortType">
    <wsdl:operation name="addOperation">
      <wsdl:input message="tns:addOperationRequest"/>
      <wsdl:output message="tns:addOperationResponse"/>
    </wsdl:operation>
    <wsdl:operation name="addOperationRole">
      <wsdl:input message="tns:addOperationRoleRequest"/>
      <wsdl:output message="tns:addOperationRoleResponse"/>
    </wsdl:operation>
    <wsdl:operation name="addOperationAuthorizedSoF">
      <wsdl:input message="tns:addOperationAuthorizedSoFRequest"/>
      <wsdl:output message="tns:addOperationAuthorizedSoFResponse"/>
    </wsdl:operation>
  </wsdl:portType>

  <wsdl:binding name="operationsSoapBinding" type="tns:OperationsPortType">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" style="document"/>
    <wsdl:operation name="addOperation">
      <soap:operation soapAction="addOperation"/>
      <wsdl:input><soap:body use="literal"/></wsdl:input>
      <wsdl:output><soap:body use="literal"/></wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addOperationRole">
      <soap:operation soapAction="addOperationRole"/>
      <wsdl:input><soap:body use="literal"/></wsdl:input>
      <wsdl:output><soap:body use="literal"/></wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="addOperationAuthorizedSoF">
      <soap:operation soapAction="addOperationAuthorizedSoF"/>
      <wsdl:input><soap:body use="literal"/></wsdl:input>
      <wsdl:output><soap:body use="literal"/></wsdl:output>
    </wsdl:operation>
  </wsdl:binding>

  <wsdl:service name="OperationsService">
    <wsdl:port name="OperationsPort" binding="tns:operationsSoapBinding">
      <soap:address location="http://localhost:8080/operations"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
