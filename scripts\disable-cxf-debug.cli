# WildFly CLI script to disable <PERSON>BUG logging and restore INFO level
# Usage: $WILDFLY_HOME/bin/jboss-cli.sh --connect --file=disable-cxf-debug.cli

# Remove DEBUG loggers to restore default INFO level
/subsystem=logging/logger=org.apache.cxf:remove
/subsystem=logging/logger=com.paymobile.admin:remove
/subsystem=logging/logger=javax.xml.ws:remove
/subsystem=logging/logger=javax.xml.soap:remove
/subsystem=logging/logger=com.sun.xml.ws:remove

# Reload the server configuration
reload

echo "DEBUG logging disabled, restored to INFO level"
