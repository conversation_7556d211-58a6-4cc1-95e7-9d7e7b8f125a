# paymobile-operation-admin (JDK 8, WildFly 15)

Production-ready Maven multi-module project to automate adding new PayMobile operations end-to-end via SOAP (JAX-WS, javax.*) and Oracle DB updates, deployable on WildFly 15. Idempotent, configurable, and compatible with PayMobile DB patterns and JNDI.

## Modules

- `admin-service` (jar): SOAP clients (wsimport), orchestration service, DAO layer, DTOs
- `admin-cli` (jar): Command-line tool (shaded) for provisioning from JSON
- `admin-springweb` (jar): Spring Boot REST API with Swagger UI
- `admin-web` (war): JAX-RS REST API for WildFly 15 (legacy)

## Request Flow

See [docs/request-flow.md](docs/request-flow.md) for a sequence diagram of the client-to-database flow.

## Build (JDK 8 only)

```bash
mvn -q -DskipTests clean package
```

## CLI Usage

```bash
java -jar admin-cli/target/admin-cli-1.0.0-SNAPSHOT-shaded.jar \
  --file=examples/dttj.json \
  --props=conf/application.properties
```

## Spring Boot Deployment

1. Package and run the application:

```bash
mvn -pl admin-springweb -am package
java -jar admin-springweb/target/admin-springweb-1.0.0-SNAPSHOT.jar \
  --paymobile.admin.props=conf/application.properties
```

2. Open Swagger UI at `http://localhost:8080/swagger-ui.html` to invoke
   `POST /api/operations/provision`.

## WildFly 15 Deployment (legacy)

1. Ensure Oracle driver module is installed on WildFly (module name `com.oracle.ojdbc`)
2. Configure Datasource with JNDI name `java:/PayMobileDS` pointing to PayMobile DB
3. Deploy `admin-web/target/admin-web.war` to WildFly 15
4. Invoke REST `POST /admin-web/api/operations/provision`

## Configuration

`conf/application.properties` (env overrides supported):

```properties
# SOAP
operations.wsdlUrl=http://localhost:8080/operations?wsdl
operations.endpointUrl=
operations.connectTimeoutMs=15000
operations.readTimeoutMs=30000

# Requester defaults
requester.accessMedium=WEB
requester.domainId=0
requester.accessType=USERNAME
requester.accessValue=ESG_ADMIN
requester.password=CHANGE_ME
requester.externalSessionId=${RANDOM_UUID}

# DB (CLI)
db.url=*******************************************
db.username=PAYMOBILE4
db.password=CHANGE_ME
db.driver=oracle.jdbc.OracleDriver
db.upsertEnabled=false

# DB (Server)
db.jndiName=java:/PayMobileDS

# Behavior
continueOnError=false
log.soapPayloads=false
```

## Idempotency & DB Patterns

- Existence checks before INSERTs
- Optional UPSERT via `db.upsertEnabled=true`
- Uses PayMobile LASTUPDATE formula:

```sql
ROUND((SYSDATE - TO_DATE('1970-01-01','YYYY-MM-DD')) * 86400)
```

- JNDI DataSource `java:/PayMobileDS` in server mode, exact match with PayMobile
- JDBC in CLI mode with Oracle driver

## Testing

- Unit tests should cover:
  - SOAP calls mapping (addOperation, addOperationRole x2, addOperationAuthorizedSoF)
  - DAO idempotency logic for PARAMETER and PARAMETERPROPERTY
  - Orchestration happy path + failure handling

## Notes

- javax.* JAX-WS only (no Jakarta)
- Logs redact passwords and secrets
- No hardcoded secrets; use properties/env only

