# WildFly CLI script to enable DEBUG logging for CXF and PayMobile components
# Usage: $WILDFLY_HOME/bin/jboss-cli.sh --connect --file=enable-cxf-debug.cli

# Enable DEBUG logging for Apache CXF
/subsystem=logging/logger=org.apache.cxf:add(level=DEBUG)

# Enable DEBUG logging for PayMobile admin components
/subsystem=logging/logger=com.paymobile.admin:add(level=DEBUG)

# Enable DEBUG logging for JAX-WS
/subsystem=logging/logger=javax.xml.ws:add(level=DEBUG)

# Enable DEBUG logging for SOAP
/subsystem=logging/logger=javax.xml.soap:add(level=DEBUG)

# Enable DEBUG logging for WSDL
/subsystem=logging/logger=com.sun.xml.ws:add(level=DEBUG)

# Reload the server configuration
reload

echo "DEBUG logging enabled for CXF and PayMobile components"
echo "Check server.log for detailed CXF service construction logs"
