package com.paymobile.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;

@Schema(description = "Definition of an operation to be provisioned")
public class OperationDefinition implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "Service code owning the operation", example = "EMONEY")
    private String serviceCode;
    @Schema(description = "Operation code to create", example = "PAY")
    private String operationCode;
    @Schema(description = "Lifecycle state", example = "ENABLED")
    private String state;
    @Schema(description = "Optional comment")
    private String comment;
    private List<I18nEntry> i18n;
    private List<RoleEntry> roles;
    private List<SofDefinition> authorizedSoFList;
    private List<ParameterEntry> parameterEntries;
    private List<ParameterPropertyEntry> parameterPropertyEntries;

    public String getServiceCode() { return serviceCode; }
    public void setServiceCode(String serviceCode) { this.serviceCode = serviceCode; }
    public String getOperationCode() { return operationCode; }
    public void setOperationCode(String operationCode) { this.operationCode = operationCode; }
    public String getState() { return state; }
    public void setState(String state) { this.state = state; }
    public String getComment() { return comment; }
    public void setComment(String comment) { this.comment = comment; }
    public List<I18nEntry> getI18n() { return i18n; }
    public void setI18n(List<I18nEntry> i18n) { this.i18n = i18n; }
    public List<RoleEntry> getRoles() { return roles; }
    public void setRoles(List<RoleEntry> roles) { this.roles = roles; }
    public List<SofDefinition> getAuthorizedSoFList() { return authorizedSoFList; }
    public void setAuthorizedSoFList(List<SofDefinition> authorizedSoFList) { this.authorizedSoFList = authorizedSoFList; }
    public List<ParameterEntry> getParameterEntries() { return parameterEntries; }
    public void setParameterEntries(List<ParameterEntry> parameterEntries) { this.parameterEntries = parameterEntries; }
    public List<ParameterPropertyEntry> getParameterPropertyEntries() { return parameterPropertyEntries; }
    public void setParameterPropertyEntries(List<ParameterPropertyEntry> parameterPropertyEntries) { this.parameterPropertyEntries = parameterPropertyEntries; }
}
