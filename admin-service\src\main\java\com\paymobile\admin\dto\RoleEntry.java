package com.paymobile.admin.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;

@Schema(description = "Role attached to an operation")
public class RoleEntry implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "Role code", example = "REQUESTER")
    private String roleCode; // REQUESTER or SENDER
    @Schema(description = "Optional role comment")
    private String comment;

    public String getRoleCode() { return roleCode; }
    public void setRoleCode(String roleCode) { this.roleCode = roleCode; }
    public String getComment() { return comment; }
    public void setComment(String comment) { this.comment = comment; }
}
