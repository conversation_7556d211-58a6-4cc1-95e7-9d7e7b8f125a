package com.paymobile.admin.dto;

import java.io.Serializable;

public class Requester implements Serializable {
    private static final long serialVersionUID = 1L;

    private String accessMedium;     // "WEB" or "SOAP"
    private int domainId;
    private String accessType;       // "USERNAME"
    private String accessValue;      // username string
    private String externalSessionId;// optional UUID
    private String password;         // ONLY for addOperationAuthorizedSoF

    public String getAccessMedium() { return accessMedium; }
    public void setAccessMedium(String accessMedium) { this.accessMedium = accessMedium; }
    public int getDomainId() { return domainId; }
    public void setDomainId(int domainId) { this.domainId = domainId; }
    public String getAccessType() { return accessType; }
    public void setAccessType(String accessType) { this.accessType = accessType; }
    public String getAccessValue() { return accessValue; }
    public void setAccessValue(String accessValue) { this.accessValue = accessValue; }
    public String getExternalSessionId() { return externalSessionId; }
    public void setExternalSessionId(String externalSessionId) { this.externalSessionId = externalSessionId; }
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
}
