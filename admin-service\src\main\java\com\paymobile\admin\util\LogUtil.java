package com.paymobile.admin.util;

public final class LogUtil {
    private LogUtil() {}

    public static String redactSecrets(String s) {
        if (s == null) return null;
        return s
            .replaceAll("(?i)(password\\s*[=:]\\s*)([^&\\s]+)", "$1******")
            .replaceAll("(?i)(Authorization\\s*:\\s*Bearer\\s*)([A-Za-z0-9._-]+)", "$1******");
    }

    public static String safe(String s) {
        return redactSecrets(s);
    }
}
