package com.paymobile.admin.springweb;

import com.paymobile.admin.config.ApplicationProperties;
import com.paymobile.admin.dao.ParameterDao;
import com.paymobile.admin.service.OperationProvisioningService;
import com.paymobile.admin.soap.OperationsSoapClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.sql.Connection;

@Configuration
public class AppConfig {

    @Bean
    public ApplicationProperties applicationProperties(@Value("${paymobile.admin.props:}") String path) {
        return ApplicationProperties.load(path);
    }

    @Bean
    public ParameterDao parameterDao(ApplicationProperties props, DataSource dataSource) {
        return new ParameterDao(props) {
            @Override
            protected Connection getConnection(boolean serverMode) throws Exception {
                return dataSource.getConnection();
            }
        };
    }

    @Bean
    public OperationsSoapClient operationsSoapClient(ApplicationProperties props) throws Exception {
        return new OperationsSoapClient(props);
    }

    @Bean
    public OperationProvisioningService operationProvisioningService(ApplicationProperties props,
                                                                     OperationsSoapClient soapClient,
                                                                     ParameterDao parameterDao) {
        return new OperationProvisioningService(props, soapClient, parameterDao, true);
    }
}
