---
type: "agent_requested"
description: "paymobile-operation-admin"
---
Target Java 8 for all compilation and runtime work; the entire Maven multi‑module project (admin-service, admin-cli, admin-springweb, admin-web) is designed around JDK 8 and WildFly 15

Use Spring Boot for the REST layer in admin-springweb; package and run with
mvn -pl admin-springweb -am package followed by
java -jar admin-springweb/target/admin-springweb-1.0.0-SNAPSHOT.jar --paymobile.admin.props=conf/application.properties

For the legacy admin-web module, deploy the generated WAR to WildFly 15, ensure the Oracle JDBC module com.oracle.ojdbc is present, configure the java:/PayMobileDS datasource, and expose the REST endpoint at /admin-web/api/operations/provision

Centralize configuration in conf/application.properties (support environment overrides) and do not hardcode secrets; logs must redact sensitive values

SOAP integrations must remain javax.* JAX‑WS (no Jakarta replacements) and use the existing idempotent DB patterns (existence checks, optional UPSERT, java:/PayMobileDS)

When expanding or fixing features, ensure unit tests cover SOAP mappings, DAO idempotency, and orchestration happy/failure paths